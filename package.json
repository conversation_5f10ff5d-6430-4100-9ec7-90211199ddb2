{
  "name": "buddychip",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "build:analyze": "ANALYZE=true next build",
    "start": "next start",
    "lint": "next lint",
    "ingest-tweets": "node scripts/ingest-tweets.js",
    "ingest-tweets:all": "node scripts/ingest-tweets.js --url=http://localhost:3000 --token=$SUPABASE_SERVICE_ROLE_KEY"
  },
  "dependencies": {
    "@ai-sdk/openai": "^1.3.22",
    "@next/bundle-analyzer": "^15.3.3",
    "@radix-ui/react-checkbox": "^1.3.2",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-dropdown-menu": "^2.1.15",
    "@radix-ui/react-label": "^2.1.7",
    "@radix-ui/react-progress": "^1.1.7",
    "@radix-ui/react-scroll-area": "^1.2.9",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-separator": "^1.1.7",
    "@radix-ui/react-slot": "^1.2.3",
    "@radix-ui/react-tabs": "^1.1.12",
    "@solana/buffer-layout-utils": "^0.2.0",
    "@solana/spl-token": "^0.4.13",
    "@solana/wallet-adapter-base": "^0.9.26",
    "@solana/wallet-adapter-react": "^0.15.38",
    "@solana/wallet-adapter-react-ui": "^0.9.38",
    "@solana/wallet-adapter-walletconnect": "^0.1.20",
    "@solana/wallet-adapter-wallets": "^0.19.36",
    "@solana/web3.js": "^1.98.2",
    "@supabase/ssr": "^0.6.1",
    "@supabase/supabase-js": "^2.49.8",
    "@tanstack/react-query": "^5.79.0",
    "@tanstack/react-query-devtools": "^5.79.0",
    "@types/react-syntax-highlighter": "^15.5.13",
    "ai": "^4.3.16",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "date-fns": "^3.6.0",
    "framer-motion": "^12.15.0",
    "lucide-react": "^0.510.0",
    "next": "15.3.2",
    "next-themes": "^0.4.6",
    "openai": "^4.104.0",
    "prismjs": "1.30.0",
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-markdown": "^10.1.0",
    "react-syntax-highlighter": "^15.6.1",
    "sentiment": "^5.0.2",
    "sonner": "^2.0.3",
    "tailwind-merge": "^3.3.0",
    "zod": "^3.25.42"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3.3.1",
    "@tailwindcss/postcss": "^4.1.8",
    "@types/node": "^22.15.27",
    "@types/react": "^19.1.6",
    "@types/react-dom": "^19.1.5",
    "@types/sentiment": "^5.0.4",
    "eslint": "^9.27.0",
    "eslint-config-next": "15.3.2",
    "pino-pretty": "^13.0.0",
    "tailwindcss": "^4.1.8",
    "tw-animate-css": "^1.3.2",
    "typescript": "^5.8.3"
  }
}

{
  "overrides": {
    "bigint-buffer": "npm:@trufflesuite/bigint-buffer@^1.0.0"
  }
}
