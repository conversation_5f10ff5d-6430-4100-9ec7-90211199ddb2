'use client';

import { ReactNode, Suspense, useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamic imports for heavy Solana wallet components with proper error handling
const SolanaWalletProvider = dynamic(
  () => import('./solana-wallet-provider').then((mod) => ({ default: mod.default })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        <span className="ml-2 text-sm text-muted-foreground">Loading wallet...</span>
      </div>
    ),
  }
);

interface Web3ProviderProps {
  children: ReactNode;
}

// Main Web3Provider component with optimized loading and client-side only rendering
export function Web3Provider({ children }: Web3ProviderProps) {
  const [isMounted, setIsMounted] = useState(false);

  // Ensure this only runs on the client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything on the server side
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        <span className="ml-2 text-sm text-muted-foreground">Initializing Web3...</span>
      </div>
    );
  }

  return (
    <Suspense fallback={
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        <span className="ml-2 text-sm text-muted-foreground">Initializing Web3...</span>
      </div>
    }>
      <SolanaWalletProvider>
        {children}
      </SolanaWalletProvider>
    </Suspense>
  );
}


