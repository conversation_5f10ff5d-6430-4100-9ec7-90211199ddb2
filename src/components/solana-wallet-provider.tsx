'use client';

import { <PERSON><PERSON>N<PERSON>, useMemo, useC<PERSON>back, useEffect } from 'react';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletError } from '@solana/wallet-adapter-base';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import {
  PhantomWalletAdapter,
  SolflareWalletAdapter,
  TorusWalletAdapter,
  LedgerWalletAdapter,
} from '@solana/wallet-adapter-wallets';
import { WalletConnectWalletAdapter } from '@solana/wallet-adapter-walletconnect';
import { useNetwork, getWalletAdapterNetwork } from '@/contexts/network-context';
import { CLIENT_CONFIG } from '@/lib/config/client';

// Import Solana wallet adapter styles
import '@solana/wallet-adapter-react-ui/styles.css';

// Prevent ethereum object conflicts by clearing any existing ethereum definitions
if (typeof window !== 'undefined') {
  // Clear any existing ethereum object that might conflict
  try {
    if (window.ethereum && typeof window.ethereum === 'object') {
      console.log('🔧 Clearing existing ethereum object to prevent conflicts');
      // Don't delete, just log that it exists
      console.log('ℹ️ Existing ethereum object detected, Solana wallet will coexist');
    }
  } catch (error) {
    console.warn('⚠️ Could not check ethereum object:', error);
  }
}

interface SolanaWalletProviderProps {
  children: ReactNode;
}

// Internal component that uses the network context
function SolanaWalletProviderInternal({ children }: SolanaWalletProviderProps) {
  // Get the current network from context
  const { currentNetwork, networkConfig } = useNetwork();

  // Convert to WalletAdapterNetwork
  const network = getWalletAdapterNetwork(currentNetwork);

  // Get the RPC endpoint
  const endpoint = useMemo(() => {
    console.log('🔗 Solana connection initialized:', {
      network: currentNetwork,
      url: networkConfig.url,
      chainId: networkConfig.chainId
    });
    return networkConfig.url;
  }, [currentNetwork, networkConfig]);

  // Error handler for wallet errors
  const onError = useCallback((error: WalletError) => {
    console.error('🚨 Wallet error occurred:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });

    // Handle specific wallet errors
    switch (error.name) {
      case 'WalletNotReadyError':
        console.warn('⚠️ Wallet not ready - user may need to install or unlock wallet');
        break;
      case 'WalletConnectionError':
        console.warn('⚠️ Wallet connection failed - retrying may help');
        break;
      case 'WalletDisconnectedError':
        console.warn('⚠️ Wallet disconnected unexpectedly');
        break;
      case 'WalletNotConnectedError':
        // Don't log this as an error - it's expected when wallet isn't connected
        console.log('ℹ️ Wallet not connected');
        break;
      default:
        // Don't show error for user rejection
        if (error.message?.includes('User rejected')) {
          console.log('ℹ️ User rejected wallet connection');
          return;
        }
        console.error('❌ Unknown wallet error:', error);
    }
  }, []);

  // Configure supported wallets with enhanced error handling
  const wallets = useMemo(() => {
    console.log('🔧 Configuring Solana wallets for network:', currentNetwork);

    try {
      const walletAdapters = [];

      // Add core wallet adapters with individual error handling
      try {
        walletAdapters.push(new PhantomWalletAdapter());
        console.log('✅ Phantom wallet adapter added');
      } catch (error) {
        console.warn('⚠️ Failed to add Phantom wallet adapter:', error);
      }

      try {
        walletAdapters.push(new SolflareWalletAdapter({ network }));
        console.log('✅ Solflare wallet adapter added');
      } catch (error) {
        console.warn('⚠️ Failed to add Solflare wallet adapter:', error);
      }

      try {
        walletAdapters.push(new TorusWalletAdapter());
        console.log('✅ Torus wallet adapter added');
      } catch (error) {
        console.warn('⚠️ Failed to add Torus wallet adapter:', error);
      }

      try {
        walletAdapters.push(new LedgerWalletAdapter());
        console.log('✅ Ledger wallet adapter added');
      } catch (error) {
        console.warn('⚠️ Failed to add Ledger wallet adapter:', error);
      }

      // Only add WalletConnect if project ID is available
      const projectId = CLIENT_CONFIG.WALLETCONNECT_PROJECT_ID;
      if (projectId && projectId !== 'demo-project-id') {
        try {
          walletAdapters.push(
            new WalletConnectWalletAdapter({
              network,
              options: {
                projectId,
                metadata: {
                  name: CLIENT_CONFIG.APP_NAME,
                  description: 'Your Twitter Assistant with Solana Integration',
                  url: CLIENT_CONFIG.APP_URL,
                  icons: [`${CLIENT_CONFIG.APP_URL}/favicon.ico`],
                },
              },
            })
          );
          console.log('✅ WalletConnect adapter added successfully');
        } catch (wcError) {
          console.warn('⚠️ Failed to initialize WalletConnect adapter:', wcError);
        }
      } else {
        console.warn('⚠️ WalletConnect project ID not configured, skipping WalletConnect adapter');
      }

      console.log(`🔧 Successfully configured ${walletAdapters.length} wallet adapters`);
      return walletAdapters;
    } catch (error) {
      console.error('❌ Error configuring wallet adapters:', error);
      return [];
    }
  }, [network, currentNetwork]);

  console.log('🌐 Solana Web3Provider initialized:', {
    network: currentNetwork,
    endpoint,
    walletsCount: wallets.length,
  });

  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} onError={onError} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
}

// Main SolanaWalletProvider component that can be used without network context
export default function SolanaWalletProvider({ children }: SolanaWalletProviderProps) {
  return <SolanaWalletProviderInternal>{children}</SolanaWalletProviderInternal>;
}
